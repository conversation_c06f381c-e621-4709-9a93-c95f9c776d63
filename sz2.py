n, m = map(int, input().split())
deg = [0] * (n + 1)  # 节点编号从 1 开始
for _ in range(m):
    u, v = map(int, input().split())
    deg[u] += 1
    deg[v] += 1

res = 0
all_f_values = []  # 存储所有f值
for i in range(1, n + 1):
    for j in range(1, n + 1):
        x = deg[i]
        y = deg[j]
        # 计算 f(x, y) = (x⊗y)(x|y)(x&y)，这里假设⊗表示按位异或^
        f = (x ^ y) * (x | y) * (x & y)
        res += f
        all_f_values.append((i, j, x, y, f))  # 记录节点对和对应的f值

print("所有f(x,y)的值:")
for pair in all_f_values:
    print(f"节点对 ({pair[0]},{pair[1]}): 度数({pair[2]},{pair[3]}) -> f={pair[4]}")

print(f"\n总和: {res}")    