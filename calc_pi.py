# Calculate Pi using the <PERSON><PERSON>ni<PERSON> formula

def calculate_pi_leibniz(iterations: int) -> float:
    pi = 0.0
    for i in range(iterations):
        pi += ((-1) ** i) / (2 * i + 1)
    return pi * 4

if __name__ == "__main__":
    n = 1000000  # Number of iterations for better accuracy
    pi_value = calculate_pi_leibniz(n)
    print(f"Calculated Pi (Leibniz, {n} iterations): {pi_value}")
