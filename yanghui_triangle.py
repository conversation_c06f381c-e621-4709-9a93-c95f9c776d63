#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
杨辉三角形生成器
Pascal's Triangle Generator

杨辉三角形是一个数字三角形，每一行的数字都是上一行相邻两个数字的和。
第n行有n+1个数字，第一个和最后一个数字都是1。
"""


def generate_yanghui_triangle(n):
    """
    生成杨辉三角形的前n行
    
    Args:
        n (int): 要生成的行数
        
    Returns:
        list: 包含n行杨辉三角形数据的列表
    """
    if n <= 0:
        return []
    
    triangle = []
    
    for i in range(n):
        row = [1]  # 每行第一个数字都是1
        
        # 计算中间的数字
        if i > 0:
            for j in range(1, i):
                # 当前位置的数字 = 上一行j-1位置 + 上一行j位置
                row.append(triangle[i-1][j-1] + triangle[i-1][j])
            
            row.append(1)  # 每行最后一个数字都是1
        
        triangle.append(row)
    
    return triangle


def print_yanghui_triangle(n, align_center=True):
    """
    打印杨辉三角形
    
    Args:
        n (int): 要打印的行数
        align_center (bool): 是否居中对齐
    """
    triangle = generate_yanghui_triangle(n)
    
    if not triangle:
        print("请输入大于0的行数")
        return
    
    if align_center:
        # 计算最后一行的宽度用于居中对齐
        last_row = triangle[-1]
        max_width = sum(len(str(num)) + 1 for num in last_row)
        
        for i, row in enumerate(triangle):
            # 将数字转换为字符串并用空格连接
            row_str = ' '.join(str(num) for num in row)
            # 计算需要的前导空格数量
            padding = (max_width - len(row_str)) // 2
            print(' ' * padding + row_str)
    else:
        # 简单打印，不居中对齐
        for row in triangle:
            print(' '.join(str(num) for num in row))


def get_yanghui_value(row, col):
    """
    获取杨辉三角形指定位置的值
    使用组合数公式: C(n,k) = n! / (k! * (n-k)!)
    
    Args:
        row (int): 行号（从0开始）
        col (int): 列号（从0开始）
        
    Returns:
        int: 指定位置的值，如果位置无效返回0
    """
    if col < 0 or col > row:
        return 0
    
    if col == 0 or col == row:
        return 1
    
    # 使用组合数公式计算
    result = 1
    for i in range(min(col, row - col)):
        result = result * (row - i) // (i + 1)
    
    return result


def print_yanghui_properties(n):
    """
    打印杨辉三角形的一些有趣性质
    
    Args:
        n (int): 行数
    """
    print(f"\n杨辉三角形前{n}行的性质：")
    print("=" * 40)
    
    triangle = generate_yanghui_triangle(n)
    
    # 每行数字之和
    print("每行数字之和：")
    for i, row in enumerate(triangle):
        row_sum = sum(row)
        print(f"第{i+1}行: {row_sum} = 2^{i}")
    
    # 对称性
    print(f"\n对称性验证（第{n}行）：")
    if n > 0:
        last_row = triangle[-1]
        is_symmetric = last_row == last_row[::-1]
        print(f"第{n}行是否对称: {is_symmetric}")
    
    # 组合数性质
    print(f"\n组合数性质验证：")
    if n >= 5:
        row_idx = 4  # 第5行
        print(f"第{row_idx+1}行: {triangle[row_idx]}")
        print("对应组合数: ", end="")
        for col in range(len(triangle[row_idx])):
            print(f"C({row_idx},{col})={get_yanghui_value(row_idx, col)}", end=" ")
        print()


def main():
    """主函数"""
    print("杨辉三角形生成器")
    print("=" * 30)
    
    try:
        # 获取用户输入
        n = int(input("请输入要生成的杨辉三角形行数: "))
        
        if n <= 0:
            print("行数必须大于0")
            return
        
        if n > 20:
            confirm = input(f"您要生成{n}行，数字可能会很大，是否继续？(y/n): ")
            if confirm.lower() != 'y':
                return
        
        print(f"\n杨辉三角形前{n}行：")
        print("-" * 30)
        
        # 打印杨辉三角形
        print_yanghui_triangle(n, align_center=True)
        
        # 显示性质
        if n <= 10:
            print_yanghui_properties(n)
        
        # 提供查询功能
        print(f"\n您可以查询指定位置的值")
        while True:
            try:
                query = input(f"请输入行号和列号（格式：行 列，行列都从1开始，输入q退出）: ")
                if query.lower() == 'q':
                    break
                
                row, col = map(int, query.split())
                if row < 1 or row > n:
                    print(f"行号必须在1到{n}之间")
                    continue
                
                value = get_yanghui_value(row-1, col-1)
                if value == 0:
                    print(f"位置({row}, {col})无效")
                else:
                    print(f"第{row}行第{col}列的值是: {value}")
                    
            except ValueError:
                print("输入格式错误，请输入两个整数")
            except KeyboardInterrupt:
                break
        
    except ValueError:
        print("请输入有效的整数")
    except KeyboardInterrupt:
        print("\n程序已退出")


if __name__ == "__main__":
    main()
